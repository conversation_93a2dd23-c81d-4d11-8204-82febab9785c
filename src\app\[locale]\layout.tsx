import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale } from 'next-intl/server';

import { Header } from '@/components/layout/header';
import { HtmlAttributesProvider } from '@/components/providers/html-attributes-provider';
import { StructuredData } from '@/components/seo/structured-data';
import {
  CulturalProvider,
  CulturalCSSVariables,
} from '@/components/ui/cultural-provider';
import { RTLProvider } from '@/components/ui/rtl-provider';
import { locales, languageConfig, type Locale } from '@/i18n';
import { generateLocalizedMetadata } from '@/lib/seo-config';

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: { locale: string };
}

export function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 动态生成每个语言的元数据
export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  // 验证语言参数
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  // 使用新的SEO配置生成元数据
  return generateLocalizedMetadata(locale as Locale);
}

export default async function LocaleLayout({
  children,
  params: { locale },
}: LocaleLayoutProps) {
  // 验证语言参数
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  // 启用静态渲染
  setRequestLocale(locale);

  // 获取消息
  const messages = await getMessages();

  // 获取语言配置
  const config = languageConfig[locale as Locale];
  // const isRTL = rtlLocales.includes(locale); // 暂时未使用

  // 根据语言选择字体类
  const getFontClass = (fontFamily: string, locale: string) => {
    switch (fontFamily) {
      case 'chinese':
        // 根据具体的中文locale选择字体
        return locale === 'zh-TW' ? 'font-noto-sans-tc' : 'font-noto-sans-sc';
      case 'japanese':
        return 'font-noto-sans-jp';
      case 'hindi':
        return 'font-noto-sans';
      default:
        return 'font-inter';
    }
  };

  return (
    <NextIntlClientProvider messages={messages}>
      <HtmlAttributesProvider />
      <CulturalProvider locale={locale as Locale}>
        <RTLProvider locale={locale as Locale}>
          <CulturalCSSVariables />
          <StructuredData type='website' />
          <StructuredData type='organization' />
          <div
            className={`min-h-screen bg-background ${getFontClass(config.fontFamily, locale)} `}
          >
            <Header />
            <main>{children}</main>
          </div>
        </RTLProvider>
      </CulturalProvider>
    </NextIntlClientProvider>
  );
}
